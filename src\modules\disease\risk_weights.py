"""
风险因素权重配置管理系统

实现风险因素权重的加载、验证和管理功能。
"""

import yaml
import logging
from pathlib import Path
from typing import Dict, Any, Optional, Union, Tuple
from dataclasses import dataclass, field
from datetime import datetime

from .risk_factors import RiskFactorType


logger = logging.getLogger(__name__)


@dataclass
class WeightConfig:
    """权重配置数据类"""
    value: float
    confidence_interval: Optional[Tuple[float, float]] = None
    reference: Optional[str] = None
    description: Optional[str] = None
    category: Optional[str] = None
    evidence_level: Optional[str] = None
    calculation_method: str = "boolean"
    
    # 连续型风险因素的额外参数
    baseline: Optional[float] = None
    per_unit_increase: Optional[float] = None
    per_hour_increase: Optional[float] = None
    per_point_decrease: Optional[float] = None
    max_effect: Optional[float] = None
    formula: Optional[str] = None
    
    def __post_init__(self):
        """验证权重配置"""
        if self.value <= 0:
            raise ValueError("权重值必须大于0")
        
        if self.confidence_interval:
            if len(self.confidence_interval) != 2:
                raise ValueError("置信区间必须包含两个值")
            if self.confidence_interval[0] >= self.confidence_interval[1]:
                raise ValueError("置信区间下限必须小于上限")


class RiskFactorWeights:
    """风险因素权重管理类"""
    
    def __init__(self, config_file: Optional[Path] = None):
        """
        初始化权重管理器
        
        Args:
            config_file: 配置文件路径，如果为None则使用默认配置
        """
        self.config_file = config_file
        self.weights: Dict[RiskFactorType, WeightConfig] = {}
        self.risk_stratification: Dict[str, Any] = {}
        self.age_adjustment: Dict[str, Any] = {}
        self.gender_adjustment: Dict[str, Any] = {}
        self.validation_rules: Dict[str, Any] = {}
        self.metadata: Dict[str, Any] = {}
        
        # 加载配置
        self._load_config()
    
    def _load_config(self):
        """加载权重配置"""
        if self.config_file is None:
            # 使用默认配置文件
            default_path = Path(__file__).parent.parent.parent.parent / "data" / "risk_factor_weights" / "default_weights.yaml"
            self.config_file = default_path
        
        if not self.config_file.exists():
            raise FileNotFoundError(f"权重配置文件不存在: {self.config_file}")
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
            
            self._parse_config(config_data)
            self._validate_config()
            
            logger.info(f"成功加载权重配置: {self.config_file}")
            
        except Exception as e:
            logger.error(f"加载权重配置失败: {e}")
            raise
    
    def _parse_config(self, config_data: Dict[str, Any]):
        """解析配置数据"""
        # 解析元数据
        risk_weights = config_data.get("risk_factor_weights", {})
        self.metadata = {
            "version": risk_weights.get("version"),
            "source": risk_weights.get("source"),
            "description": risk_weights.get("description"),
            "last_updated": risk_weights.get("last_updated")
        }
        
        # 解析权重配置
        weights_data = risk_weights.get("weights", {})
        for factor_name, weight_data in weights_data.items():
            try:
                factor_type = RiskFactorType(factor_name)
                
                # 处理置信区间
                ci = weight_data.get("confidence_interval")
                if ci and isinstance(ci, list) and len(ci) == 2:
                    ci = tuple(ci)
                
                # 对于连续型风险因素，如果没有直接的value，使用默认值1.0
                value = weight_data.get("value", 1.0)

                weight_config = WeightConfig(
                    value=value,
                    confidence_interval=ci,
                    reference=weight_data.get("reference"),
                    description=weight_data.get("description"),
                    category=weight_data.get("category"),
                    evidence_level=weight_data.get("evidence_level"),
                    calculation_method=weight_data.get("calculation_method", "boolean"),
                    baseline=weight_data.get("baseline"),
                    per_unit_increase=weight_data.get("per_unit_increase"),
                    per_hour_increase=weight_data.get("per_hour_increase"),
                    per_point_decrease=weight_data.get("per_point_decrease"),
                    max_effect=weight_data.get("max_effect"),
                    formula=weight_data.get("formula")
                )
                
                self.weights[factor_type] = weight_config
                
            except ValueError as e:
                logger.warning(f"跳过未知风险因素类型: {factor_name}")
                continue
        
        # 解析其他配置
        self.risk_stratification = config_data.get("risk_stratification", {})
        self.age_adjustment = config_data.get("age_adjustment", {})
        self.gender_adjustment = config_data.get("gender_adjustment", {})
        self.validation_rules = config_data.get("validation_rules", {})
    
    def _validate_config(self):
        """验证配置完整性"""
        # 检查必需的风险因素是否都有权重
        required_factors = {
            RiskFactorType.FAMILY_HISTORY,
            RiskFactorType.IBD,
            RiskFactorType.BMI,
            RiskFactorType.DIABETES,
            RiskFactorType.SMOKING
        }
        
        missing_factors = required_factors - set(self.weights.keys())
        if missing_factors:
            logger.warning(f"缺少必需风险因素的权重配置: {[f.value for f in missing_factors]}")
        
        # 验证权重值范围
        weight_range = self.validation_rules.get("weight_range", {})
        min_weight = weight_range.get("min", 0.1)
        max_weight = weight_range.get("max", 10.0)
        
        for factor_type, weight_config in self.weights.items():
            if not (min_weight <= weight_config.value <= max_weight):
                logger.warning(
                    f"权重值超出范围 {factor_type.value}: {weight_config.value} "
                    f"(范围: {min_weight}-{max_weight})"
                )
    
    def get_weight(self, factor_type: RiskFactorType) -> Optional[WeightConfig]:
        """获取指定风险因素的权重配置"""
        return self.weights.get(factor_type)
    
    def get_weight_value(self, factor_type: RiskFactorType) -> float:
        """获取指定风险因素的权重值"""
        weight_config = self.get_weight(factor_type)
        return weight_config.value if weight_config else 1.0
    
    def has_weight(self, factor_type: RiskFactorType) -> bool:
        """检查是否存在指定风险因素的权重"""
        return factor_type in self.weights
    
    def get_all_weights(self) -> Dict[RiskFactorType, WeightConfig]:
        """获取所有权重配置"""
        return self.weights.copy()
    
    def calculate_continuous_weight(
        self, 
        factor_type: RiskFactorType, 
        value: float
    ) -> float:
        """
        计算连续型风险因素的权重
        
        Args:
            factor_type: 风险因素类型
            value: 风险因素值
            
        Returns:
            float: 计算得出的权重
        """
        weight_config = self.get_weight(factor_type)
        if not weight_config or weight_config.calculation_method != "continuous":
            return weight_config.value if weight_config else 1.0
        
        if factor_type == RiskFactorType.BMI:
            return self._calculate_bmi_weight(value, weight_config)
        elif factor_type == RiskFactorType.SEDENTARY_LIFESTYLE:
            return self._calculate_sedentary_weight(value, weight_config)
        elif factor_type == RiskFactorType.ALCOHOL_CONSUMPTION:
            return self._calculate_alcohol_weight(value, weight_config)
        elif factor_type == RiskFactorType.DIET_QUALITY:
            return self._calculate_diet_weight(value, weight_config)
        else:
            return weight_config.value
    
    def _calculate_bmi_weight(self, bmi: float, config: WeightConfig) -> float:
        """计算BMI权重"""
        if config.baseline is None or config.per_unit_increase is None:
            return config.value
        
        weight = 1.0 + max(0, bmi - config.baseline) * config.per_unit_increase
        
        if config.max_effect:
            weight = min(weight, config.max_effect)
        
        return weight
    
    def _calculate_sedentary_weight(self, hours: float, config: WeightConfig) -> float:
        """计算久坐时间权重"""
        if config.baseline is None or config.per_hour_increase is None:
            return config.value
        
        weight = 1.0 + max(0, hours - config.baseline) * config.per_hour_increase
        
        if config.max_effect:
            weight = min(weight, config.max_effect)
        
        return weight
    
    def _calculate_alcohol_weight(self, units: float, config: WeightConfig) -> float:
        """计算酒精消费权重"""
        if config.per_unit_increase is None:
            return config.value
        
        weight = 1.0 + units * config.per_unit_increase
        
        if config.max_effect:
            weight = min(weight, config.max_effect)
        
        return weight
    
    def _calculate_diet_weight(self, score: float, config: WeightConfig) -> float:
        """计算饮食质量权重"""
        if config.baseline is None or config.per_point_decrease is None:
            return config.value
        
        weight = 1.0 + max(0, config.baseline - score) * config.per_point_decrease
        
        if config.max_effect:
            weight = min(weight, config.max_effect)
        
        return weight
    
    def get_risk_stratification_thresholds(self) -> Dict[str, float]:
        """获取风险分层阈值"""
        return {
            "low_risk": self.risk_stratification.get("low_risk", {}).get("threshold", 1.5),
            "moderate_risk_min": self.risk_stratification.get("moderate_risk", {}).get("threshold_min", 1.5),
            "moderate_risk_max": self.risk_stratification.get("moderate_risk", {}).get("threshold_max", 3.0),
            "high_risk": self.risk_stratification.get("high_risk", {}).get("threshold", 3.0)
        }
    
    def get_age_adjustment_params(self) -> Dict[str, Any]:
        """获取年龄调整参数"""
        return self.age_adjustment.copy()
    
    def get_gender_adjustment_params(self) -> Dict[str, Any]:
        """获取性别调整参数"""
        return self.gender_adjustment.copy()
    
    def get_metadata(self) -> Dict[str, Any]:
        """获取配置元数据"""
        return self.metadata.copy()
    
    def reload_config(self):
        """重新加载配置"""
        self.weights.clear()
        self._load_config()
    
    def save_config(self, file_path: Path):
        """保存配置到文件"""
        config_data = {
            "risk_factor_weights": {
                **self.metadata,
                "weights": {}
            },
            "risk_stratification": self.risk_stratification,
            "age_adjustment": self.age_adjustment,
            "gender_adjustment": self.gender_adjustment,
            "validation_rules": self.validation_rules
        }
        
        # 转换权重配置
        for factor_type, weight_config in self.weights.items():
            weight_data = {
                "value": weight_config.value,
                "calculation_method": weight_config.calculation_method
            }
            
            if weight_config.confidence_interval:
                weight_data["confidence_interval"] = list(weight_config.confidence_interval)
            
            for attr in ["reference", "description", "category", "evidence_level", 
                        "baseline", "per_unit_increase", "per_hour_increase", 
                        "per_point_decrease", "max_effect", "formula"]:
                value = getattr(weight_config, attr)
                if value is not None:
                    weight_data[attr] = value
            
            config_data["risk_factor_weights"]["weights"][factor_type.value] = weight_data
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True)
            
            logger.info(f"配置已保存到: {file_path}")
            
        except Exception as e:
            logger.error(f"保存配置失败: {e}")
            raise
    
    def __repr__(self) -> str:
        """字符串表示"""
        return (
            f"RiskFactorWeights(factors={len(self.weights)}, "
            f"version={self.metadata.get('version', 'unknown')})"
        )
